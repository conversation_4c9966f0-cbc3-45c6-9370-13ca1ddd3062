version: "3"

services:
  octobot:
    image: drakkarsoftware/octobot:stable
    volumes:
       - ./logs:/octobot/logs
       - ./backtesting:/octobot/backtesting
       - ./tentacles:/octobot/tentacles
       - ./user:/octobot/user
    ports:
       - ${PORT:-80}:${PORT:-5001}
    restart: always
  
  watchtower:
    image: containrrr/watchtower
    restart: always
    command: --cleanup --include-restarting
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
