[loggers]
keys=root

[handlers]
keys=console<PERSON><PERSON><PERSON>,fileHandler

[formatters]
keys=consoleFormatter,fileFormatter

[logger_root]
level=DEBUG
handlers=consoleHandler,fileHandler

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=consoleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=handlers.RotatingFileHandler
level=DEBUG
formatter=fileFormatter
args=('logs/OctoBot.log', 'a', 24000000, 20)

[formatter_consoleFormatter]
class=colorlog.ColoredFormatter
format=%(log_color)s %(asctime)s %(levelname)-8s %(name)-20s %(message)s

[formatter_fileFormatter]
format=%(asctime)-16s %(levelname)-6s %(name)-20s %(filename)-s:%(lineno)-8s %(message)s
datefmt=%Y-%m-%d %H:%M:%S
