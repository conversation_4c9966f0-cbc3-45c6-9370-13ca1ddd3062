# OctoBot source code

In this repository is containing the highest abstraction level code of OctoBot.

- The trading logic code can be found on the [OctoBot Trading dependency](https://github.com/Drakkar-Software/OctoBot-trading)
- OctoBot strategies, external connectors and UI are located in the [OctoBot Tentacles](https://github.com/Drakkar-Software/OctoBot-tentacles) repository

More info on OctoBot's architecture on the [OctoBot website](https://www.octobot.cloud/en/guides/octobot-developers-environment/architecture?utm_source=github&utm_medium=dk&utm_campaign=regular_open_source_content&utm_content=architecture)
