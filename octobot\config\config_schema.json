{"type": "object", "properties": {"DEBUG": {"type": "boolean"}, "DEV-MODE": {"type": "boolean"}, "SAVE_EVALUATIONS": {"type": "boolean"}, "accepted_terms": {"type": "boolean"}, "distribution": {"type": "string"}, "profile": {"type": "string"}, "community": {"type": "object", "properties": {"bot_id": {"type": "string"}, "supabase.auth.token": {"type": "string"}, "environment": {"type": "string"}}}, "backtesting": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string"}}}}, "data-collector": {"type": "object", "properties": {"symbol": {"type": "string"}}}, "exchanges": {"type": "object", "patternProperties": {"^.*$": {"type": "object"}}}, "metrics": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "metrics-bot-id": {"type": "string"}}}, "community-token": {"type": "string"}, "notification": {"type": "object", "properties": {"global-info": {"type": "boolean"}, "price-alerts": {"type": "boolean"}, "trades": {"type": "boolean"}, "other": {"type": "boolean"}, "notification-type": {"type": "array", "items": {"type": "string"}}}, "required": ["global-info", "price-alerts", "trades", "notification-type"]}, "services": {"type": "object", "patternProperties": {"^.*$": {"type": "object"}}}, "watched_symbols": {"type": "array", "items": {"type": "string"}}}, "required": ["backtesting", "exchanges", "services", "notification"], "additionalProperties": {"type": "string"}}