name: "OctoBot-Release"

on:
  push:
    tags:
      - '*'

jobs:
  release-jobs:
    name: Create release jobs
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set Environment Variables
      run: |
        echo "VERSION=${GITHUB_REF##*/}" >> $GITHUB_ENV

    - name: Create release on OctoBot-Tentacles
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
      with:
        tag_name: ${{ env.VERSION }}
        release_name: Version - ${{ env.VERSION }}
        draft: false
        prerelease: false
        commitish: master
        repo: OctoBot-Tentacles

# TODO uncomment when adding tests to OctoBot-Binary
#    - name: Wait for tentacles build done
#      uses: fountainhead/action-wait-for-check@v1.0.0
#      id: wait-for-build
#      with:
#        token: ${{ secrets.AUTH_TOKEN }}
#        checkName: "Done"
#        ref: ${{ github.sha }}
#        timeoutSeconds: 3600
#
#    - name: Trigger fail when Main failed
#      if: steps.wait-for-build.outputs.conclusion == 'failure'
#      run: exit 1

    - name: Create release on OctoBot-Binary
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.AUTH_TOKEN }}
      with:
        tag_name: ${{ env.VERSION }}
        release_name: Version - ${{ env.VERSION }}
        draft: false
        prerelease: false
        commitish: master
        repo: OctoBot-Binary

  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs:
      - release-jobs
    if: ${{ failure() }}

    steps:
      - name: Notify discord
        uses: sarisia/actions-status-discord@v1
        with:
          status: Failure
          webhook: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}
