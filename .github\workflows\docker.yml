name: OctoBot-Docker
on:
  push:
    branches:
      - "master"
      - "dev"
    tags:
      - "*"
  pull_request:

jobs:
  lint:
    name: ubuntu-latest - Docker - lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Run hadolint
        uses: reviewdog/action-hadolint@v1
        with:
          github_token: ${{ secrets.github_token }}
          hadolint_ignore: DL3013 DL3008

  build_test_push:
    needs: lint
    name: ubuntu-latest - Docker - build & test & push
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set Environment Variables
        run: |
          OWNER="$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]' | tr -d '-')"
          IMG=octobot
          echo "VERSION=${GITHUB_REF##*/}" >> $GITHUB_ENV
          echo "IMAGE=${OWNER}/${IMG}" >> $GITHUB_ENV
          echo "LATEST=latest" >> $GITHUB_ENV
          echo "STAGING=staging" >> $GITHUB_ENV
          echo "STABLE=stable" >> $GITHUB_ENV
          echo "TEST=test" >> $GITHUB_ENV
          echo "SHA=${GITHUB_SHA}" >> $GITHUB_ENV
          echo "CONTAINER_NAME=octobot" >> $GITHUB_ENV
          echo "CHECK_TENTACLE_CONTAINER_TIME=10" >> $GITHUB_ENV
          echo "WAIT_CONTAINER_TIME=80" >> $GITHUB_ENV

      - name: Wait for tentacles
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags')
        uses: fountainhead/action-wait-for-check@v1.0.0
        id: wait-for-tentacles
        with:
          token: ${{ secrets.AUTH_TOKEN }}
          checkName: "ubuntu-latestx64 - Python - 3.10 - Upload"
          ref: ${{ github.ref }}
          repo: OctoBot-Tentacles
          timeoutSeconds: 3600

      - name: Trigger fail when Tentacles failed
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags') && steps.wait-for-tentacles.outputs.conclusion == 'failure'
        run: exit 1

      - name: Set up QEMU
        id: qemu-setup
        uses: docker/setup-qemu-action@master
        with:
          platforms: all

      - name: Print available platforms
        run: echo ${{ steps.qemu.outputs.platforms }}

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@master
        with:
          driver: docker-container
          use: true

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to DockerHub
        if: github.event_name == 'push'
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build latest
        if: github.event_name != 'push'
        uses: docker/build-push-action@master
        with:
          context: .
          builder: ${{ steps.buildx.outputs.name }}
          platforms: linux/amd64
          # Using "load: true" forces the docker driver.
          # Unfortunately, the "docker" driver does not support
          # multi-platform builds.
          load: true
          push: false
          tags: ${{ env.IMAGE }}:${{ env.SHA }}
          build-args: |
            TENTACLES_URL_TAG=${{ env.LATEST }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Docker build test container
        if: github.event_name != 'push'
        run: |
          docker build -f ./tests/Dockerfile --build-arg OCTOBOT_IMAGE=${{ env.IMAGE }}:${{ env.SHA }} -t ${{ env.IMAGE }}:${{ env.TEST }} .

      - name: run tests in docker
        if: github.event_name != 'push'
        run: |
          docker run -i -e TENTACLES_REPOSITORY=dev-tentacles -e TENTACLES_URL_TAG=${GITHUB_HEAD_REF////_} -e DISABLE_SENTRY=True ${{ env.IMAGE }}:${{ env.TEST }}

      - name: run and check health after start
        if: github.event_name != 'push'
        run: |
          echo Start OctoBot docker with branche_name tentacles package
          docker run -id -e TENTACLES_REPOSITORY=dev-tentacles -e TENTACLES_URL_TAG=${GITHUB_HEAD_REF////_} -e DISABLE_SENTRY=True --name ${{ env.CONTAINER_NAME }} ${{ env.IMAGE }}:${{ env.LATEST }}
          sleep ${{ env.CHECK_TENTACLE_CONTAINER_TIME }}
          if docker logs ${{ env.CONTAINER_NAME }} | grep "octobot_tentacles_manager.api.util.tentacles_management Failed to download file at url :" ; then
              docker rm -f ${{ env.CONTAINER_NAME }}
              echo Restarting docker with latest tentacle package...
              docker run -id -e TENTACLES_URL_TAG=${{ env.LATEST }} -e DISABLE_SENTRY=True --name ${{ env.CONTAINER_NAME }} ${{ env.IMAGE }}:${{ env.LATEST }}
          fi
          sleep ${{ env.WAIT_CONTAINER_TIME }}
          docker logs ${{ env.CONTAINER_NAME }}
          docker inspect ${{ env.CONTAINER_NAME }} | jq '.[].State.Health.Status' | grep "healthy"

      - name: Build and push latest
        if: github.event_name == 'push' && !startsWith(github.ref, 'refs/tags') && github.ref == 'refs/heads/dev'
        uses: docker/build-push-action@master
        with:
          context: .
          builder: ${{ steps.buildx.outputs.name }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.IMAGE }}:${{ env.LATEST }}
          build-args: |
            TENTACLES_URL_TAG=${{ env.LATEST }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Build and push staging
        if: github.event_name == 'push' && github.ref == 'refs/heads/master'
        uses: docker/build-push-action@master
        with:
          context: .
          builder: ${{ steps.buildx.outputs.name }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ env.IMAGE }}:${{ env.STAGING }}
          build-args: |
            TENTACLES_URL_TAG=${{ env.STABLE }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

      - name: Build and push on tag
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags')
        uses: docker/build-push-action@master
        with:
          context: .
          file: ./Dockerfile
          builder: ${{ steps.buildx.outputs.name }}
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ env.IMAGE }}:${{ env.LATEST }}
            ${{ env.IMAGE }}:${{ env.STABLE }}
            ${{ env.IMAGE }}:${{ env.VERSION }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache

  notify:
    if: ${{ failure() }}
    needs:
      - lint
      - build_test_push
    uses: Drakkar-Software/.github/.github/workflows/failure_notify_workflow.yml@master
    secrets:
      DISCORD_GITHUB_WEBHOOK: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}


  notify-dockerhub-update:
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags')
    name: Notify dockerhub update
    runs-on: ubuntu-latest
    needs:
      - lint
      - build_test_push

    steps:
      - name: Notify discord
        uses: sarisia/actions-status-discord@v1
        with:
          description: "@here a new tag has been published on Docker Hub, docker bots can be updated."
          webhook: ${{ secrets.DISCORD_GITHUB_WEBHOOK }}
