---
name: Bug report
about: Create a report to help us improve

---

## Step 1: Have you search for this issue before posting it?

If you have discovered a bug in the bot, please [search our issue tracker](https://github.com/Drakkar-Software/OctoBot/issues?q=is%3Aissue). 
If it hasn't been reported, please create a new issue.

## Step 2: Describe your environment
  * OS : [Windows, Ubuntu, Debian, Raspbian...] 
  * Python Version: _____ (`python -V`)
  * In case you are not using a binary version:
    * Branch: Master | Dev
    * Last Commit ID: _____ (`git log --format="%H" -n 1`)
 
## Step 3: Describe the problem:
**Describe the bug**
A clear and concise description of what the bug is.

**Expected behavior**
A clear and concise description of what you expected to happen.

### Steps to reproduce:

  1. _____
  2. _____
  3. _____
  
### Observed Results:

  * What happened?
  * What did you expect to happen?

### Relevant code exceptions or logs:
If applicable, add screenshots to help explain your problem.
 
```
// paste your log here
```
