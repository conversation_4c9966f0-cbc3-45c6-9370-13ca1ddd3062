#  This file is part of OctoBot (https://github.com/Drakkar-Software/OctoBot)
#  Copyright (c) 2025 Drakkar-Software, All rights reserved.
#
#  OctoBot is free software; you can redistribute it and/or
#  modify it under the terms of the GNU General Public License
#  as published by the Free Software Foundation; either
#  version 3.0 of the License, or (at your option) any later version.
#
#  OctoBot is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
#  General Public License for more details.
#
#  You should have received a copy of the GNU General Public
#  License along with OctoBot. If not, see <https://www.gnu.org/licenses/>.

from octobot.community.feeds import abstract_feed
from octobot.community.feeds.abstract_feed import (
    AbstractFeed,
)
from octobot.community.feeds import community_ws_feed
from octobot.community.feeds.community_ws_feed import (
    CommunityWSFeed,
)
from octobot.community.feeds import community_mqtt_feed
from octobot.community.feeds.community_mqtt_feed import (
    CommunityMQTTFeed,
)
from octobot.community.feeds import community_supabase_feed
from octobot.community.feeds.community_supabase_feed import (
    CommunitySupabaseFeed,
)
from octobot.community.feeds import feed_factory
from octobot.community.feeds.feed_factory import (
    community_feed_factory,
)

__all__ = [
    "AbstractFeed",
    "CommunityWSFeed",
    "CommunityMQTTFeed",
    "CommunitySupabaseFeed",
    "community_feed_factory",
]
