# Drakkar-Software requirements
OctoBot-Commons==1.9.82
OctoBot-Trading==2.4.215
OctoBot-Evaluators==1.9.7
OctoBot-Tentacles-Manager==2.9.16
OctoBot-Services==1.6.26
OctoBot-Backtesting==1.9.7
Async-Channel==2.2.1
trading-backend==1.2.41

## Others
colorlog==6.8.0
requests==2.32.5
urllib3 # required by requests, used in imports: make sure it's always available
packaging==23.2
python-dotenv==1.0.0
setuptools==79.0.1  # warning: setuptools>=80 breaks easy_install, need to find an alternative not to break installs
# see https://community.palantir.com/t/important-update-on-setuptools-pinning-the-version-below-80-0-0/3872

# Community
websockets==15.0.1 # used by supabase, a recent version is required, see https://github.com/supabase/realtime-py/blob/main/pyproject.toml
gmqtt==0.7.0
pgpy==0.6.0
clickhouse-connect==0.8.18

# Error tracking
sentry-sdk==2.35.0  # always make sure sentry_aiohttp_transport.py keep working

# Supabase  ensure supabase_backend_tests keep passing when updating any of those
supabase==2.18.1   # Supabase client
supabase_auth     # Supabase authenticated API (required by supabase and enforced to allow direct import)
postgrest  # Supabase posgres calls (required by supabase and enforced to allow direct import)

# async http requests
aiohttp==3.12.15
# updating to aiodns==3.2.0 is incompatible (and failing CI)
# raises RuntimeError: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
aiodns==3.1.1   # used by aiohttp

# used by ccxt for protobuf "websockets" such as mexc
# lock protobuf to avoid using .rc versions
protobuf==5.29.5
